# 量化股票软件Tus - 部署说明

## 🎉 打包成功！

您的量化股票软件已成功打包为单个exe文件：`dist/量化股票软件Tus.exe`

## 📁 文件结构

```
项目目录/
├── dist/
│   └── 量化股票软件Tus.exe    # 主程序文件（约100-200MB）
├── 一键打包.bat               # 打包脚本
├── 完整打包脚本.py            # 详细打包脚本
├── 测试exe文件.bat            # 测试脚本
└── 打包说明.md               # 本文件
```

## 🚀 部署方法

### 方法1：直接使用（推荐）
1. 将 `dist/量化股票软件Tus.exe` 复制到目标计算机
2. 双击运行即可启动

### 方法2：完整部署
1. 将整个 `dist` 目录复制到目标计算机
2. 在目标计算机上双击运行 `量化股票软件Tus.exe`

## 💻 系统要求

- **操作系统**：Windows 10/11 (64位)
- **内存**：至少 4GB RAM
- **存储空间**：至少 500MB 可用空间
- **网络**：稳定的互联网连接（用于数据获取）

## 🔧 首次运行

1. **启动软件**：双击 `量化股票软件Tus.exe`
2. **登录界面**：会自动显示Tushare登录/注册界面
3. **账户处理**：
   - 如果有账户：输入手机号登录
   - 如果没有账户：输入手机号会自动注册
4. **验证码**：输入图片验证码
5. **主程序**：登录成功后自动启动股票看图软件

## ✨ 主要功能

### 🔐 登录系统
- Tushare账户自动注册/登录
- 智能验证码识别
- 多浏览器支持（Chrome/Edge）
- 自动Token获取和保存

### 📊 股票分析
- 实时股票数据获取
- K线图和技术指标显示
- 多种技术指标计算
- 自定义时间范围查询

### 🔄 回测系统
- 单股票策略回测
- 多股票批量回测
- 板块股票批量加载
- 详细回测报告和分析

### 📈 实时监控
- 股票价格实时更新
- 多股票同时监控
- 策略信号提醒
- 自动交易功能（可选）

### 🏢 板块功能
- **主板**：沪深主板股票
- **中小板**：深圳中小板股票  
- **创业板**：深圳创业板股票
- **科创板**：上海科创板股票
- **北交所**：北京证券交易所股票
- **指数成分股**：沪深300、中证1000、上证50

## ⚙️ 高级特性

### 🚦 API限流控制
- 每分钟最多450次API调用
- 智能等待和重试机制
- 避免超出Tushare限制

### 💾 智能缓存
- 自动数据缓存和压缩
- 减少重复API调用
- 提高数据加载速度

### 🛡️ 错误处理
- 网络异常自动重试
- 获取失败友好提示
- 浏览器驱动自动切换

## 🔍 测试方法

### 快速测试
```bash
# 运行测试脚本
测试exe文件.bat
```

### 手动测试
1. 双击 `量化股票软件Tus.exe`
2. 检查登录界面是否正常显示
3. 输入测试手机号（可以是虚拟号码）
4. 验证各功能模块是否正常

## 🐛 故障排除

### 常见问题

**问题1：程序无法启动**
- 检查是否被杀毒软件拦截
- 确认系统是64位Windows
- 尝试以管理员身份运行

**问题2：登录失败**
- 检查网络连接
- 确认验证码输入正确
- 尝试刷新验证码

**问题3：数据获取慢**
- 正常现象，受API限制影响
- 耐心等待，避免频繁操作
- 检查网络连接稳定性

**问题4：浏览器相关错误**
- 程序会自动尝试Chrome和Edge
- 确保系统安装了其中一个浏览器
- 检查浏览器版本是否过旧

### 日志查看
- 程序运行时会显示详细日志
- 根据日志信息诊断问题
- 记录错误信息便于排查

## 📋 功能清单

### ✅ 已实现功能
- [x] 登录注册界面
- [x] 股票数据获取和显示
- [x] K线图和技术指标
- [x] 单股票回测系统
- [x] 多股票回测系统
- [x] 板块股票加载
- [x] API限流控制
- [x] 智能缓存系统
- [x] 实时数据监控
- [x] 策略模板库
- [x] 自定义策略编写
- [x] 回测结果分析
- [x] 网页交易功能
- [x] 错误处理优化

### 🔄 特殊处理
- [x] 获取数据每分钟限制量控制
- [x] 多股票回测板块加载功能
- [x] 获取股票列表失败显示"欢迎使用"
- [x] 市场数据获取和板块功能完整集成

## 📞 技术支持

### 自助排查
1. 查看程序运行日志
2. 检查网络连接状态
3. 确认系统环境要求
4. 参考故障排除指南

### 功能建议
- 软件内置反馈功能
- 持续优化和更新
- 用户体验改进

## 🔄 更新说明

### 当前版本特性
- 集成登录和主程序
- 优化API调用控制
- 增强板块功能
- 改进错误处理
- 提升用户体验

### 后续计划
- 添加更多技术指标
- 扩展策略模板
- 优化数据获取速度
- 增加更多板块分类
- 提升界面友好性

---

## 🎯 总结

您的量化股票软件已成功打包为单个exe文件，包含了所有要求的功能：

1. ✅ **API限流控制** - 每分钟最多450次调用
2. ✅ **板块股票加载** - 支持多种板块批量回测
3. ✅ **友好错误处理** - 失败时显示"欢迎使用"
4. ✅ **完整功能集成** - 市场数据、回测、监控一体化

现在您可以将 `dist/量化股票软件Tus.exe` 部署到任何Windows计算机上使用！

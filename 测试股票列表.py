#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试股票列表获取功能
"""

import os
import sys

def test_token():
    """测试token文件"""
    token_file = "tushare_token.txt"
    print("=== Token测试 ===")
    
    if os.path.exists(token_file):
        try:
            with open(token_file, 'r', encoding='utf-8') as f:
                token = f.read().strip()
            print(f"Token文件存在，长度: {len(token)}")
            if len(token) > 10:
                print(f"Token前10位: {token[:10]}...")
                return token
            else:
                print("Token长度不足，可能无效")
                return None
        except Exception as e:
            print(f"读取Token文件失败: {e}")
            return None
    else:
        print("Token文件不存在")
        return None

def test_market_data_manager():
    """测试市场数据管理器"""
    print("\n=== 市场数据管理器测试 ===")
    
    try:
        from 市场数据管理 import MarketDataManager
        
        # 读取token
        token = test_token()
        
        # 初始化市场数据管理器
        manager = MarketDataManager(
            cache_dir="market_data_cache",
            token=token
        )
        
        print(f"市场数据管理器初始化成功")
        print(f"API对象: {manager.pro is not None}")
        
        # 测试获取股票列表
        print("\n正在获取股票列表...")
        stock_list = manager.get_all_stock_list()
        
        print(f"获取到 {len(stock_list)} 只股票")
        
        if len(stock_list) > 0:
            print("\n前5只股票:")
            print(stock_list.head()[['ts_code', 'name', 'industry']].to_string())
            
            if 'sector' in stock_list.columns:
                print("\n各板块股票数量:")
                sector_counts = stock_list['sector'].value_counts()
                print(sector_counts.head(10))
            else:
                print("没有板块信息")
        
        return manager
        
    except Exception as e:
        print(f"市场数据管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_sector_stocks():
    """测试板块股票获取"""
    print("\n=== 板块股票测试 ===")
    
    manager = test_market_data_manager()
    if not manager:
        return
    
    sectors = ["主板", "中小板", "创业板", "科创板"]
    
    for sector in sectors:
        try:
            stocks = manager.get_sector_stocks_enhanced(sector)
            print(f"{sector}: {len(stocks)} 只股票")
            if len(stocks) > 0:
                print(f"  前3只: {stocks[:3]}")
        except Exception as e:
            print(f"{sector} 获取失败: {e}")

def main():
    """主函数"""
    print("股票列表功能测试")
    print("=" * 50)
    
    # 测试token
    token = test_token()
    
    # 测试市场数据管理器
    test_market_data_manager()
    
    # 测试板块股票
    test_sector_stocks()
    
    print("\n测试完成")

if __name__ == "__main__":
    main()

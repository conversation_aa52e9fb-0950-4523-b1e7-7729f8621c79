#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试股票看图软件的市场数据功能
"""

import tkinter as tk
import sys
import os

def test_stock_viewer():
    """测试股票看图软件"""
    try:
        # 导入股票看图软件
        from 股票看图软件_增强版 import EnhancedStockViewerApp
        
        print("正在启动股票看图软件...")
        
        # 创建主窗口
        root = tk.Tk()
        app = EnhancedStockViewerApp(root)
        
        # 检查市场数据管理器状态
        print(f"市场数据管理器: {app.market_data_manager is not None}")
        
        if app.market_data_manager:
            print("正在测试获取股票列表...")
            try:
                stock_list = app.market_data_manager.get_all_stock_list()
                print(f"获取到 {len(stock_list)} 只股票")
                
                if len(stock_list) > 0:
                    print("前5只股票:")
                    print(stock_list.head()[['ts_code', 'name']].to_string())
                
            except Exception as e:
                print(f"获取股票列表失败: {e}")
        
        print("股票看图软件启动成功，请在界面中测试市场数据功能")
        
        # 运行主循环
        root.mainloop()
        
    except Exception as e:
        print(f"启动股票看图软件失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_stock_viewer()

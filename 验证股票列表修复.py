#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证股票列表修复是否有效
"""

import os
import sys

def test_stock_list_fix():
    """测试股票列表修复"""
    print("=== 验证股票列表修复 ===")
    
    try:
        # 导入市场数据管理器
        from 市场数据管理 import MarketDataManager
        
        # 读取token
        token_file = "tushare_token.txt"
        if os.path.exists(token_file):
            with open(token_file, 'r', encoding='utf-8') as f:
                token = f.read().strip()
            print(f"✓ Token文件存在，长度: {len(token)}")
        else:
            print("❌ Token文件不存在")
            return False
        
        # 初始化市场数据管理器
        manager = MarketDataManager(
            cache_dir="market_data_cache",
            token=token
        )
        
        print(f"✓ 市场数据管理器初始化成功")
        print(f"✓ API对象: {manager.pro is not None}")
        
        # 获取股票列表
        print("\n正在获取股票列表...")
        stock_list = manager.get_all_stock_list()
        
        print(f"✓ 获取到 {len(stock_list)} 只股票")
        
        # 检查必要的列
        required_columns = ['ts_code', 'name', 'sector', 'market_name', 'industry', 'area']
        missing_columns = []
        
        for col in required_columns:
            if col not in stock_list.columns:
                missing_columns.append(col)
        
        if missing_columns:
            print(f"❌ 缺少列: {missing_columns}")
            return False
        else:
            print("✓ 所有必要列都存在")
        
        # 检查板块分布
        if len(stock_list) > 0:
            print("\n板块分布:")
            sector_counts = stock_list['sector'].value_counts()
            for sector, count in sector_counts.head(10).items():
                print(f"  {sector}: {count} 只")
            
            print(f"\n前5只股票:")
            display_cols = ['ts_code', 'name', 'sector', 'market_name']
            print(stock_list[display_cols].head().to_string(index=False))
        
        # 测试板块筛选功能
        print("\n=== 测试板块筛选功能 ===")
        sectors_to_test = ["主板", "创业板", "科创板"]
        
        for sector in sectors_to_test:
            try:
                sector_stocks = manager.get_sector_stocks_enhanced(sector)
                print(f"✓ {sector}: {len(sector_stocks)} 只股票")
                if len(sector_stocks) > 0:
                    print(f"  前3只: {sector_stocks[:3]}")
            except Exception as e:
                print(f"❌ {sector} 筛选失败: {e}")
                return False
        
        print("\n🎉 股票列表修复验证成功！")
        print("✓ 股票数量正常")
        print("✓ 必要列完整")
        print("✓ 板块功能正常")
        print("✓ 可以正常同步到多股票回测")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_stock_backtest_integration():
    """测试多股票回测集成"""
    print("\n=== 测试多股票回测集成 ===")
    
    try:
        from 市场数据管理 import MarketDataManager
        
        # 读取token
        token_file = "tushare_token.txt"
        with open(token_file, 'r', encoding='utf-8') as f:
            token = f.read().strip()
        
        manager = MarketDataManager(
            cache_dir="market_data_cache",
            token=token
        )
        
        # 测试获取板块股票用于回测
        test_sectors = ["主板", "创业板"]
        
        for sector in test_sectors:
            stocks = manager.get_sector_stocks_enhanced(sector)
            if len(stocks) > 0:
                print(f"✓ {sector}板块可用于多股票回测: {len(stocks)} 只股票")
                # 取前5只作为示例
                sample_stocks = stocks[:5]
                print(f"  示例股票: {sample_stocks}")
            else:
                print(f"❌ {sector}板块无可用股票")
                return False
        
        print("✓ 多股票回测板块集成正常")
        return True
        
    except Exception as e:
        print(f"❌ 多股票回测集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("股票列表修复验证")
    print("=" * 50)
    
    # 测试股票列表修复
    if not test_stock_list_fix():
        print("\n❌ 股票列表修复验证失败")
        return False
    
    # 测试多股票回测集成
    if not test_multi_stock_backtest_integration():
        print("\n❌ 多股票回测集成测试失败")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过！")
    print("✅ 股票列表功能已修复")
    print("✅ 可以获取完整的股票列表（5000+只）")
    print("✅ 板块功能正常工作")
    print("✅ 多股票回测可以正常加载板块股票")
    print("✅ API限制处理正确（使用缓存）")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\n按回车键退出...")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 发生未预期的错误: {str(e)}")
        input("按回车键退出...")
        sys.exit(1)
    
    input("\n按回车键退出...")

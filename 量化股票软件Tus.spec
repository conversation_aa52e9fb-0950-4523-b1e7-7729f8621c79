# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 需要包含的数据文件和模块
added_files = [
    ('股票看图软件_增强版.py', '.'),
    ('回测系统.py', '.'),
    ('回测分析.py', '.'),
    ('策略模板.py', '.'),
    ('多股票回测系统.py', '.'),
    ('多股票监控管理器.py', '.'),
    ('多股票监控配置.py', '.'),
    ('技术指标库.py', '.'),
    ('市场数据管理.py', '.'),
    ('使用者监控.py', '.'),
    ('交易调度器.py', '.'),
    ('浏览器驱动管理.py', '.'),
    ('策略示例', '策略示例'),
    ('user_config', 'user_config'),
    ('market_data_cache', 'market_data_cache'),
    ('tushare_token.txt', '.'),
]

a = Analysis(
    ['登录注册.py'],  # 使用登录注册.py作为主入口
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=[
        # 基础GUI库
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'tkinter.filedialog',
        
        # 数据处理库
        'pandas',
        'numpy',
        'openpyxl',
        'xlrd',
        'xlsxwriter',
        
        # 网络和API库
        'tushare',
        'requests',
        'urllib3',
        'lxml',
        'bs4',
        'beautifulsoup4',
        
        # 图表库
        'matplotlib',
        'matplotlib.pyplot',
        'matplotlib.backends.backend_tkagg',
        'matplotlib.figure',
        'matplotlib.patches',
        'matplotlib.dates',
        'matplotlib.backends.backend_pdf',
        'seaborn',
        
        # 浏览器自动化
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.edge.service',
        'selenium.webdriver.edge.options',
        'selenium.webdriver.chrome.service',
        'selenium.webdriver.chrome.options',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support',
        'selenium.webdriver.support.expected_conditions',
        'webdriver_manager',
        'webdriver_manager.microsoft',
        'webdriver_manager.chrome',
        
        # 图像处理
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        
        # 系统和工具库
        'datetime',
        'threading',
        'json',
        'time',
        'os',
        'sys',
        'subprocess',
        'importlib',
        'importlib.util',
        'concurrent.futures',
        'queue',
        'logging',
        'warnings',
        'hashlib',
        'base64',
        'io',
        'gzip',
        'pickle',
        'collections',
        'typing',
        'pathlib',
        'platform',
        'ctypes',
        
        # 内部模块
        '股票看图软件_增强版',
        '回测系统',
        '回测分析',
        '策略模板',
        '多股票回测系统',
        '多股票监控管理器',
        '多股票监控配置',
        '技术指标库',
        '市场数据管理',
        '使用者监控',
        '交易调度器',
        '浏览器驱动管理',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的大型库
        'PyQt5',
        'PySide6',
        'PyQt6',
        'PySide2',
        'torch',
        'tensorflow',
        'cv2',
        'sklearn',
        'scipy.sparse.csgraph._validation',
        'IPython',
        'jupyter',
        'notebook',
        'spyder',
        'pytest',
        'unittest',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='量化股票软件Tus',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)

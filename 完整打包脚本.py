#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票看图软件完整打包脚本
将登录注册.py作为启动界面，打包成单个exe文件
包含所有必要的功能和依赖项
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print("✓ PyInstaller已安装")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    try:
        print("正在安装PyInstaller...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✓ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller安装失败: {e}")
        return False

def create_complete_spec_file():
    """创建完整的PyInstaller配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 需要包含的数据文件和模块
added_files = [
    ('股票看图软件_增强版.py', '.'),
    ('回测系统.py', '.'),
    ('回测分析.py', '.'),
    ('策略模板.py', '.'),
    ('多股票回测系统.py', '.'),
    ('多股票监控管理器.py', '.'),
    ('多股票监控配置.py', '.'),
    ('技术指标库.py', '.'),
    ('市场数据管理.py', '.'),
    ('使用者监控.py', '.'),
    ('交易调度器.py', '.'),
    ('浏览器驱动管理.py', '.'),
    ('策略示例', '策略示例'),
    ('user_config', 'user_config'),
    ('market_data_cache', 'market_data_cache'),
    ('tushare_token.txt', '.'),
]

a = Analysis(
    ['登录注册.py'],  # 使用登录注册.py作为主入口
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=[
        # 基础GUI库
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'tkinter.filedialog',
        
        # 数据处理库
        'pandas',
        'numpy',
        'openpyxl',
        'xlrd',
        'xlsxwriter',
        
        # 网络和API库
        'tushare',
        'requests',
        'urllib3',
        'lxml',
        'bs4',
        'beautifulsoup4',
        
        # 图表库
        'matplotlib',
        'matplotlib.pyplot',
        'matplotlib.backends.backend_tkagg',
        'matplotlib.figure',
        'matplotlib.patches',
        'matplotlib.dates',
        'matplotlib.backends.backend_pdf',
        'seaborn',
        
        # 浏览器自动化
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.edge.service',
        'selenium.webdriver.edge.options',
        'selenium.webdriver.chrome.service',
        'selenium.webdriver.chrome.options',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support',
        'selenium.webdriver.support.expected_conditions',
        'webdriver_manager',
        'webdriver_manager.microsoft',
        'webdriver_manager.chrome',
        
        # 图像处理
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        
        # 系统和工具库
        'datetime',
        'threading',
        'json',
        'time',
        'os',
        'sys',
        'subprocess',
        'importlib',
        'importlib.util',
        'concurrent.futures',
        'queue',
        'logging',
        'warnings',
        'hashlib',
        'base64',
        'io',
        'gzip',
        'pickle',
        'collections',
        'typing',
        'pathlib',
        'platform',
        'ctypes',
        
        # 内部模块
        '股票看图软件_增强版',
        '回测系统',
        '回测分析',
        '策略模板',
        '多股票回测系统',
        '多股票监控管理器',
        '多股票监控配置',
        '技术指标库',
        '市场数据管理',
        '使用者监控',
        '交易调度器',
        '浏览器驱动管理',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的大型库
        'PyQt5',
        'PySide6',
        'PyQt6',
        'PySide2',
        'torch',
        'tensorflow',
        'cv2',
        'sklearn',
        'scipy.sparse.csgraph._validation',
        'IPython',
        'jupyter',
        'notebook',
        'spyder',
        'pytest',
        'unittest',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='量化股票软件Tus',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
'''
    
    with open('量化股票软件Tus.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 完整配置文件已创建: 量化股票软件Tus.spec")

def check_required_files():
    """检查必要的文件是否存在"""
    required_files = [
        '登录注册.py',
        '股票看图软件_增强版.py',
        '回测系统.py',
        '回测分析.py',
        '策略模板.py',
        '多股票回测系统.py',
        '技术指标库.py',
        '市场数据管理.py',
        '使用者监控.py',
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✓ 所有必要文件都存在")
    return True

def create_directories():
    """创建必要的目录"""
    directories = ['user_config', 'market_data_cache', 'drivers']
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ 目录已创建: {directory}")

def create_token_file():
    """创建默认的token文件"""
    token_file = 'tushare_token.txt'
    if not os.path.exists(token_file):
        with open(token_file, 'w', encoding='utf-8') as f:
            f.write('')  # 创建空的token文件
        print(f"✓ 已创建默认token文件: {token_file}")

def build_exe():
    """构建exe文件"""
    print("\n" + "="*60)
    print("开始构建exe文件...")
    print("这可能需要几分钟时间，请耐心等待...")
    print("="*60)
    
    try:
        # 使用spec文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "量化股票软件Tus.spec"]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        
        if result.returncode == 0:
            print("✓ exe文件构建成功！")
            print(f"输出目录: {os.path.abspath('dist')}")
            return True
        else:
            print("❌ exe文件构建失败")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中出现异常: {str(e)}")
        return False

def clean_build_files():
    """清理构建过程中的临时文件"""
    print("正在清理临时文件...")
    
    dirs_to_clean = ['build', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✓ 已清理目录: {dir_name}")
            except Exception as e:
                print(f"⚠ 清理目录失败 {dir_name}: {str(e)}")

def main():
    """主函数"""
    print("=" * 60)
    print("量化股票软件Tus - 完整打包脚本")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists('登录注册.py'):
        print("❌ 未找到主程序文件: 登录注册.py")
        print("请确保在正确的目录中运行此脚本")
        return False
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            return False
    
    # 检查必要文件
    if not check_required_files():
        return False
    
    # 创建必要目录
    create_directories()
    
    # 创建token文件
    create_token_file()
    
    # 创建spec配置文件
    create_complete_spec_file()
    
    # 构建exe文件
    if not build_exe():
        return False
    
    # 清理临时文件
    clean_build_files()
    
    print("\n" + "="*60)
    print("🎉 打包完成！")
    print("="*60)
    print(f"📁 exe文件位置: dist/量化股票软件Tus.exe")
    print(f"📁 完整程序目录: dist/")
    print("\n使用说明:")
    print("1. 进入dist目录")
    print("2. 双击运行 量化股票软件Tus.exe")
    print("3. 首次运行会显示登录界面")
    print("4. 登录成功后自动启动股票看图软件")
    print("5. 软件包含完整的回测、监控和交易功能")
    print("="*60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\n按回车键退出...")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 发生未预期的错误: {str(e)}")
        input("按回车键退出...")
        sys.exit(1)
    
    input("\n按回车键退出...")

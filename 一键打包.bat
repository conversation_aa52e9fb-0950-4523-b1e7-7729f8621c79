@echo off
chcp 65001 >nul
echo ====================================
echo 量化股票软件Tus - 一键打包脚本
echo ====================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python并添加到系统PATH
    pause
    exit /b 1
)

echo ✓ Python环境正常
echo.

echo 正在运行完整打包脚本...
python 完整打包脚本.py

if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo.
echo ✓ 打包完成！
echo.
echo 📁 exe文件位置: dist\量化股票软件Tus.exe
echo.
echo 使用说明:
echo 1. 进入dist目录
echo 2. 双击运行 量化股票软件Tus.exe
echo 3. 首次运行会显示登录界面
echo 4. 登录成功后自动启动股票看图软件
echo.
pause

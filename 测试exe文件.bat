@echo off
chcp 65001 >nul
echo ====================================
echo 测试量化股票软件Tus.exe
echo ====================================
echo.

echo 检查exe文件是否存在...
if exist "dist\量化股票软件Tus.exe" (
    echo ✓ 找到exe文件: dist\量化股票软件Tus.exe
    echo.
    
    echo 获取文件信息...
    for %%A in ("dist\量化股票软件Tus.exe") do (
        echo 📁 文件大小: %%~zA 字节
        echo 📅 修改时间: %%~tA
    )
    echo.
    
    echo 是否要启动软件进行测试？
    echo 注意：这将启动登录界面
    echo.
    set /p choice="输入 Y 启动测试，任意键退出: "
    
    if /i "%choice%"=="Y" (
        echo.
        echo 正在启动量化股票软件Tus.exe...
        echo 请在弹出的登录界面中进行测试
        echo.
        start "" "dist\量化股票软件Tus.exe"
        echo ✓ 软件已启动
    ) else (
        echo 取消测试
    )
) else (
    echo ❌ 未找到exe文件
    echo 请先运行打包脚本生成exe文件
)

echo.
pause

# 量化股票软件Tus - 打包说明

## 概述

本项目将登录注册界面和股票看图软件打包成一个完整的exe文件，实现以下功能：

1. **保持获取数据每分钟的限制量** - 通过API限流控制器确保不超过Tushare API限制
2. **多股票回测，获取选择板块设置为加载板块股票** - 支持按板块加载股票进行批量回测
3. **获取股票列表失败，不要让用户知道，改成欢迎使用** - 失败时显示默认股票列表
4. **市场数据获取股票列表可以获取股票数据，并且多股票回测可以通过板块加载板块股票** - 完整的市场数据管理和板块功能

## 打包方法

### 方法1：使用一键打包脚本（推荐）

1. 双击运行 `一键打包.bat`
2. 等待打包完成
3. 在 `dist` 目录中找到 `量化股票软件Tus.exe`

### 方法2：手动运行Python脚本

```bash
python 完整打包脚本.py
```

### 方法3：直接使用PyInstaller

```bash
# 安装PyInstaller
pip install pyinstaller

# 运行打包脚本生成spec文件
python 完整打包脚本.py

# 或者直接使用生成的spec文件
pyinstaller --clean 量化股票软件Tus.spec
```

## 软件功能

### 登录界面功能
- Tushare账户注册和登录
- 自动获取和验证验证码
- 智能浏览器驱动管理（支持Chrome和Edge）
- 登录成功后自动启动主程序

### 股票看图软件功能
- 股票数据获取和显示
- K线图绘制和技术指标分析
- 单股票和多股票回测系统
- 实时数据监控
- 网页交易功能
- 策略模板和自定义策略

### 市场数据管理
- API限流控制（每分钟最多450次调用）
- 智能缓存系统
- 板块分类和股票筛选
- 数据压缩和存储优化

### 多股票回测
- 支持按板块加载股票
- 批量回测和结果分析
- 策略性能对比
- 风险指标计算

## 使用说明

### 首次使用
1. 双击运行 `量化股票软件Tus.exe`
2. 在登录界面输入手机号（如果没有账户会自动注册）
3. 输入图片验证码
4. 登录成功后自动启动股票看图软件

### 功能使用
1. **股票数据查看**：输入股票代码，选择时间范围，点击获取数据
2. **技术指标分析**：在图表上查看各种技术指标
3. **单股票回测**：选择策略，设置参数，运行回测
4. **多股票回测**：选择板块，批量加载股票，运行批量回测
5. **实时监控**：启动实时数据更新功能

### 板块功能
- **主板**：沪深主板股票
- **中小板**：深圳中小板股票
- **创业板**：深圳创业板股票
- **科创板**：上海科创板股票
- **北交所**：北京证券交易所股票
- **指数成分股**：沪深300、中证1000、上证50等

## 注意事项

### 系统要求
- Windows 10/11 64位系统
- 至少4GB内存
- 稳定的网络连接

### API限制
- 每分钟最多450次API调用
- 建议合理安排数据获取频率
- 大量数据获取时请耐心等待

### 数据缓存
- 软件会自动缓存数据以提高性能
- 缓存文件存储在 `market_data_cache` 目录
- 可以手动清理缓存文件

### 错误处理
- 获取股票列表失败时会显示"欢迎使用"
- 网络错误时会自动重试
- 浏览器驱动问题会自动切换备用方案

## 文件结构

```
量化股票软件Tus.exe          # 主程序文件
├── 登录注册界面             # 启动界面
├── 股票看图软件             # 主功能模块
├── 回测系统                # 回测引擎
├── 市场数据管理             # 数据管理
├── 多股票监控               # 监控功能
├── 技术指标库               # 指标计算
└── 策略模板                # 策略库
```

## 技术特点

### 智能缓存
- 自适应压缩算法
- 数据完整性验证
- 过期自动更新

### API限流
- 智能等待机制
- 批量请求优化
- 错误重试策略

### 用户体验
- 无控制台窗口
- 友好的错误提示
- 自动资源清理

## 故障排除

### 常见问题
1. **程序无法启动**：检查是否有杀毒软件拦截
2. **登录失败**：检查网络连接和验证码输入
3. **数据获取慢**：正常现象，受API限制影响
4. **浏览器错误**：程序会自动尝试不同浏览器

### 日志查看
- 程序运行时会在控制台输出日志信息
- 可以通过日志信息诊断问题

## 更新说明

### 版本特性
- 集成登录和主程序为单一exe文件
- 优化API调用频率控制
- 增强板块股票加载功能
- 改进错误处理机制
- 提升用户体验

### 后续计划
- 添加更多技术指标
- 扩展策略模板库
- 优化数据获取速度
- 增加更多板块分类

## 联系支持

如有问题或建议，请通过以下方式联系：
- 软件内置反馈功能
- 查看日志信息进行自助诊断
- 参考使用说明文档
